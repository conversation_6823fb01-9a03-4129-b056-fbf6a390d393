from collections import defaultdict
from django.db import connection
from rest_framework.views import APIView
from rest_framework.response import Response
import math
import json
import openpyxl
from rest_framework import status
from .serializers import (
    ScenarioMetadSerializer,ClusterDataSerializer, OutlierDetectionResponseSerializer,OutlierDetectionRequestSerializer,
    ClusterRequestSerializer,testAndControlStoreSerializer,InsertTestControlStrRequestSerializer,InsertTestControlStrResponseSerializer,GraphDataRequestSerializer,GraphDataResponseSerializer,MetricGraphResponseSerializer,MetricGraphRequestSerializer,GDCSDataRequestSerializer,
    OutlierUpdateSerializer
)
from .model_map import CLUSTER_MODEL_MAP, STR_CLUSTER_MODEL_MAP
from .models import ScenarioStatus, FileUpload
from .serializers import ScenarioMetadSerializer, ClusterDataSerializer, ClusterUpdateSerializer
from django.utils import timezone
from django.db import transaction
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from rest_framework.exceptions import ValidationError
from .model_map import CLUSTER_MODEL_MAP, APP_CLUSTER_MODEL_MAP, DE_PREOPTIMIZATION_MODEL_MAP
from .models import ScenarioStatus, ScenarioMetad, ScenarioMetad
from .serializers import ScenarioMetadSerializer, ClusterDataSerializer,StoreSelectionDropdownSerializer,StoreSelectionDataSerializer
from django.utils import timezone
from django.db import transaction
from dateutil.relativedelta import relativedelta
from datetime import datetime
from django.db.models import Sum, Avg
from rest_framework.pagination import PageNumberPagination
import pandas as pd
import numpy as np
from decimal import Decimal, InvalidOperation
from django.db.models import Q, Sum, Avg, Max

class SpaceHealthView(APIView):

    def get(self, request):
        concept = request.query_params.get("concept")
        # month = request.query_params.get("month")
        


        if not concept:
            return Response({"error": "Missing required parameters: concept"}, status=400)

        model = PREOPT_MODEL_MAP.get(concept.lower())

        if not model:
            return Response({"error": f"Invalid concept: {concept}"}, status=400)

        # Fetch the data
        try:
            data = model.objects.values(
                "grp_nm", "dpt_nm", "clss_nm", "sub_clss_nm",
                "loc_cd", "loc_nm",
                "total_lm", "spc", "gmv", "net_sls_amt",
                "mnth_avg_soh", "mnth_avg_optn_cnt", "ros", "cover",
                "gmv_per_lm", "lm_contribution_in_store"
            )
        except Exception as e:
            return Response({"error": str(e)}, status=500)

        # Now build the nested response
        result = []
        group_map = defaultdict(lambda: {"children": []})

        for row in data:
            group = row["grp_nm"] or "-"
            department = row["dpt_nm"] or "-"
            class_name = row["clss_nm"] or "-"
            sub_class = row["sub_clss_nm"] or "-"

            # Build keys for each level
            grp = group_map[group]
            grp.update({"group": group})
            dept_list = grp.setdefault("children", [])
            
            # Find or create department
            dept = next((d for d in dept_list if d["department"] == department), None)
            if not dept:
                dept = {"department": department, "children": []}
                dept_list.append(dept)

            # Find or create class
            class_list = dept["children"]
            cls = next((c for c in class_list if c["class"] == class_name), None)
            if not cls:
                cls = {"class": class_name, "children": []}
                class_list.append(cls)

            # Subclass data
            cls["children"].append({
                "subClass": sub_class,
                "storeId": row["loc_cd"],
                "storeName": row["loc_nm"],
                "lm": row["total_lm"],
                "sqft": row["spc"],
                "gmv": row["gmv"],
                "revenue": row["net_sls_amt"],
                "avgStock": row["mnth_avg_soh"],
                "optionCount": row["mnth_avg_optn_cnt"],
                "presentRos": row["ros"],
                "coverDays": row["cover"],
                "gmvPerLm": row["gmv_per_lm"],
                "lmCont": row["lm_contribution_in_store"],
            })

        result = list(group_map.values())
        return Response(result, status=200)
    

class StoreListView(APIView):

    def get(self, request):
        concept = request.query_params.get("concept")
        territory = request.query_params.get("territory")
        
        if not concept or not territory:
            return Response({"error": "Missing required parameters: concept and territory"}, status=400)

        
        table_name = f"de_{concept.lower()}_{territory.lower()}_cluster"
        
        ClusterModel = CLUSTER_MODEL_MAP.get(table_name)
        
        if not ClusterModel:
            return Response({"error": f"Unsupported cluster table: {table_name}"}, status=400)

        try:
            queryset = ClusterModel.objects.all().values("loc_cd", "loc_nm")
            return Response(list(queryset), status=200)
        except Exception as e:
            return Response({"error": f"Database error: {str(e)}"}, status=500)

class HeallthMetricsPagination(PageNumberPagination):
    """Custom pagination for HeallthMetrics endpoint with page size of 10"""
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

def parse_month_year_to_datetime(value):
    # Convert "YYYY-MM" to datetime object "YYYY-MM-01 00:00:00"
    if value and isinstance(value, str) and len(value) == 7:
        try:
            return datetime.strptime(value + '-01', '%Y-%m-%d')
        except ValueError:
            return None
    return None

class ScenarioCreateAPIView(APIView):


    def post(self, request):
        try:
            with transaction.atomic():
                uploaded_files = {}
                for key, field in {
                    'sqft_file': 'sqft_file_id',
                    'mdq_file': 'mdq_file_id',
                    'cover_file': 'cover_file_id',
                    'exclusion_file': 'exclusion_file_id'
                }.items():
                    uploaded_file = request.FILES.get(key)
                    if uploaded_file:
                        file_path = default_storage.save(uploaded_file.name, ContentFile(uploaded_file.read()))
                        file_url = default_storage.url(file_path)

                        readable_type = key.replace('_file', '').upper()

                        file_obj = FileUpload.objects.create(
                            file_name=uploaded_file.name,
                            file_type=readable_type,
                            url=file_url
                        )
                        uploaded_files[field] = file_obj.id
                    else:
                        uploaded_files[field] = None

                # Prepare and clean data
                data = request.data.copy()

                for key in ['sqft_file', 'mdq_file', 'cover_file', 'exclusion_file']:
                    data.pop(key, None)
                data.update(uploaded_files)

                # Parse dates
                for date_field in ['ref_start', 'ref_end']:
                    if date_field in data:
                        converted = parse_month_year_to_datetime(data[date_field])
                        data[date_field] = converted.date() if converted else None

                scenario_id = data.get('id')

                if scenario_id:
                    # 🔁 Update existing scenario
                    try:
                        instance = ScenarioMetad.objects.get(id=scenario_id)
                    except ScenarioMetad.DoesNotExist:
                        return Response({'error': f'Scenario with id {scenario_id} not found'}, status=404)

                    serializer = ScenarioMetadSerializer(instance, data=data, partial=True)
                else:
                    # 🆕 Create new scenario
                    serializer = ScenarioMetadSerializer(data=data)

                try:
                    serializer.is_valid(raise_exception=True)
                except ValidationError as e:
                    print("Validation errors:", serializer.errors)
                    return Response({"errors": serializer.errors}, status=400)

                scenario = serializer.save(updated_at=timezone.now())

                if not scenario_id:
                    # Only create status record on first creation
                    ScenarioStatus.objects.create(scenario=scenario)

                return Response(
                    {
                        'message': f'Scenario {"updated" if scenario_id else "created"} successfully',
                        'id': scenario.id
                    },
                    status=200 if scenario_id else 201
                )

        except Exception as e:
            import traceback
            traceback_str = traceback.format_exc()
            print("Exception traceback:", traceback_str)
            return Response({'error': str(e), 'traceback': traceback_str}, status=400)



class ClusterByLocationView(APIView):


    def post(self, request):
        # Step 1: Validate input
        print("request.data", request.data)
        input_serializer = ClusterRequestSerializer(data=request.data)
        if not input_serializer.is_valid():
            return Response(input_serializer.errors, status=400)

        validated = input_serializer.validated_data
        concept = validated["concept"]
        territory = validated["territory"]
        loc_codes = validated["loc_codes"]
        user_id = request.user.id  # Assuming auth

        # Step 2: Get structured model (app_*_str_cluster)
        str_model = STR_CLUSTER_MODEL_MAP.get(f"app_{concept.lower()}_str_cluster")
        if not str_model:
            return Response({"error": f"Unsupported cluster table for concept {concept}"}, status=400)

        # Step 3: Get raw model (de_*_cluster) for source data
        table_name = f"de_{concept.lower()}_{territory.lower()}_cluster"
        ClusterModel = CLUSTER_MODEL_MAP.get(table_name)
        if not ClusterModel:
            return Response({"error": f"Unsupported cluster table: {table_name}"}, status=400)

        # Step 4: Fetch source data (de_*)
        try:
            source_data = ClusterModel.objects.filter(loc_cd__in=loc_codes)
        except Exception as e:
            return Response({"error": f"Database error: {str(e)}"}, status=500)

        # Step 5: Conditionally insert missing rows into structured table
        timestamp = timezone.now()
        new_objects = []
        existing_locs = set(str_model.objects.filter(loc_cd__in=loc_codes).values_list("loc_cd", flat=True))
        
        for row in source_data:
            if row.loc_cd in existing_locs:
                continue  # Skip already present

            obj = str_model(
                territory_nm=row.rgn_nm,
                loc_cd=row.loc_cd,
                loc_nm=row.loc_nm,
                stnd_trrtry_nm=row.rgn_nm,
                rgn_nm=row.rgn_nm,
                revenue=row.revenue,
                units=row.units,
                gmv=row.gmv,
                total_lm=row.total_lm,
                total_customer=row.total_customer,
                total_invoice=row.total_invoice,
                area_sqft=row.area_sqft,
                cluster_num=row.cluster_num,
                volume_contribution=row.volume_contribution,
                ethnicity_contribution=row.ethnicity_contribution,
                revenue_per_sqft=row.revenue_per_sqft,
                updated_at=timestamp,
                updated_by=user_id,
                new_cluster_num=row.cluster_num
            )
            new_objects.append(obj)

        try:
            if new_objects:
                str_model.objects.bulk_create(new_objects, ignore_conflicts=True)
        except Exception as e:
            return Response({"error": f"Insert failed: {str(e)}"}, status=500)

        # Step 6: Fetch and group the data from app_*_str_cluster
        try:
            filtered_str_data = str_model.objects.filter(loc_cd__in=loc_codes)
        except Exception as e:
            return Response({"error": f"Structured data fetch error: {str(e)}"}, status=500)

        grouped = defaultdict(list)
        for row in filtered_str_data:
            ClusterDataSerializer.Meta.model = str_model
            serialized = ClusterDataSerializer(row).data
            grouped[row.cluster_num].append(serialized)

        return Response(grouped)
    
    def put(self, request):
        # Step 1: Validate input list of updates
        
        updates = request.data.get('updates', [])
        
        serializer = ClusterUpdateSerializer(data=updates, many=True)
        
        if not serializer.is_valid(): 
            return Response(serializer.errors, status=400)

        
        validated_updates = serializer.validated_data

        # Step 2: Determine the model to update (similar to post method)
        

        # user_id = request.user.id
        timestamp = timezone.now()
       
        try:
            with transaction.atomic():
                for update in validated_updates:
                    loc_cd = update['loc_cd']
                    new_cluster_num = update['cluster_num']
                    concept = update['concept']
                    
                    if not concept:
                        return Response({"error": "concept and territory query params are required"}, status=400)
                    str_model = STR_CLUSTER_MODEL_MAP.get(f"app_{concept.lower()}_str_cluster")
                    if not str_model:
                        return Response({"error": f"Unsupported cluster table for concept {concept}"}, status=400)
                    # Update the cluster_num and new_cluster_num fields
                    try:
                        str_model.objects.filter(loc_cd=loc_cd).update(
                            cluster_num=new_cluster_num,
                            new_cluster_num=new_cluster_num,
                            updated_at=timestamp,
                            # updated_by=1
                        )
                    except Exception as e:
                        print("error in saving", e)
        except Exception as e:
            return Response({"error": f"Update failed: {str(e)}"}, status=500)

        return Response({"detail": "Clusters updated successfully"})


    def delete(self, request):
        loc_cd = request.data.get("loc_cd")
        cluster_num = request.data.get("cluster_num")
        concept = request.data.get("concept")

        
        if not all([loc_cd, concept]):
            return Response({"error": "loc_cd, cluster_num, and concept are required"}, status=400)

        str_model = STR_CLUSTER_MODEL_MAP.get(f"app_{concept.lower()}_str_cluster")
        if not str_model:
            return Response({"error": f"Unsupported cluster table for concept {concept}"}, status=400)

        try:
            deleted_count, _ = str_model.objects.filter(
                loc_cd=loc_cd,
                cluster_num=cluster_num
            ).delete()

            if deleted_count == 0:
                return Response({"detail": "No matching record found."}, status=404)

            return Response({"detail": f"{deleted_count} store(s) deleted."}, status=200)

        except Exception as e:
            return Response({"error": str(e)}, status=500)

        
class StoreSelectionDropdown(APIView):
    
    def post(self, request):
        input_serializer = StoreSelectionDropdownSerializer(data=request.data)
        if not input_serializer.is_valid():
            return Response(input_serializer.errors, status=400)

        validated = input_serializer.validated_data
        concept = validated["concept"]
        territory = validated["territory"]


        table_name = f"app_{concept.lower()}_str_cluster"
        ClusterModel = APP_CLUSTER_MODEL_MAP.get(table_name)
        if not ClusterModel:
            return Response({"error": f"Unsupported cluster table: {table_name}"}, status=400)

       
        try:
            filtered_data = ClusterModel.objects.filter(territory_nm=territory)
        except Exception as e:
            return Response({"error": f"Database error: {str(e)}"}, status=500)
        
        StoreSelectionDataSerializer.Meta.model = ClusterModel
        serializer = StoreSelectionDataSerializer(filtered_data, many=True)
        formatted_data = [
            {
                "value": item["loc_cd"],
                "label": f"{item['loc_cd']} - {item['loc_nm']}"
            }
            for item in serializer.data
        ]
      
        return Response(formatted_data)


    def put(self, request):
        # Step 1: Validate input list of updates
        
        updates = request.data.get('updates', [])
        
        serializer = ClusterUpdateSerializer(data=updates, many=True)
        
        if not serializer.is_valid():
            print("serializer errors:", serializer.errors) 
            return Response(serializer.errors, status=400)

        
        validated_updates = serializer.validated_data

        # Step 2: Determine the model to update (similar to post method)
        

        # user_id = request.user.id
        timestamp = timezone.now()
       
        try:
            with transaction.atomic():
                for update in validated_updates:
                    loc_cd = update['loc_cd']
                    new_cluster_num = update['cluster_num']
                    concept = update['concept']
                    
                    if not concept:
                        return Response({"error": "concept and territory query params are required"}, status=400)
                    str_model = STR_CLUSTER_MODEL_MAP.get(f"app_{concept.lower()}_str_cluster")
                    if not str_model:
                        return Response({"error": f"Unsupported cluster table for concept {concept}"}, status=400)
                    # Update the cluster_num and new_cluster_num fields
                    try:
                        str_model.objects.filter(LOC_CD=loc_cd).update(
                            CLUSTER_NUM=new_cluster_num,
                            new_cluster_num=new_cluster_num,
                            updated_at=timestamp,
                            # updated_by=1
                        )
                    except Exception as e:
                        print("error in saving", e)
        except Exception as e:
            return Response({"error": f"Update failed: {str(e)}"}, status=500)

        return Response({"detail": "Clusters updated successfully"})


    def delete(self, request):
        loc_cd = request.data.get("loc_cd")
        cluster_num = request.data.get("cluster_num")
        concept = request.data.get("concept")

        
        if not all([loc_cd, concept]):
            return Response({"error": "loc_cd, cluster_num, and concept are required"}, status=400)

        str_model = STR_CLUSTER_MODEL_MAP.get(f"app_{concept.lower()}_str_cluster")
        if not str_model:
            return Response({"error": f"Unsupported cluster table for concept {concept}"}, status=400)

        try:
            deleted_count, _ = str_model.objects.filter(
                LOC_CD=loc_cd,
                CLUSTER_NUM=cluster_num
            ).delete()

            if deleted_count == 0:
                return Response({"detail": "No matching record found."}, status=404)

            return Response({"detail": f"{deleted_count} store(s) deleted."}, status=200)

        except Exception as e:
            return Response({"error": str(e)}, status=500)


# class OutlierAPIView(APIView):
#     def get(self, request):
#         sub_clss_nm = request.query_params.get('subclass')
#         loc_cd = request.query_params.get('store')
#         filters = {}
#         if sub_clss_nm:
#             filters['sub_clss_nm'] = sub_clss_nm
#         if loc_cd:
#             filters['loc_cd'] = loc_cd
#         outliers = AppHbPreopt.objects.filter(**filters)
#         # Only return the required fields
#         data = outliers.values(
#             'lm_contribution_in_store',  # LM_CONTRIBUTION_IN_STORE
#             'sub_clss_nm',               # SUB_CLSS_NM
#             'loc_cd',                    # LOC_CD
#             'month',                     # MONTH
#             'total_lm',                  # TOTAL_LM
#             'gmv_per_day',               # GMV_PER_DAY
#             'suggested_total_lm'         # SUGGESTED_TOTAL_LM
#         )
#         return Response(list(data))
class OutlierAPIView(APIView):
    def get(self, request):
        """Get outlier records with optional filtering by subclass and store"""
        try:
            sub_clss_nm = request.query_params.get('subclass')
            loc_cd = request.query_params.get('store')

            filters = {}
            if sub_clss_nm:
                filters['sub_clss_nm'] = sub_clss_nm
            if loc_cd:
                try:
                    filters['loc_cd'] = int(loc_cd)
                except ValueError:
                    return Response(
                        {
                            'error': 'Invalid store parameter',
                            'details': 'store parameter must be a valid number'
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

            outliers = AppHbPreopt.objects.filter(**filters)

            if not outliers.exists():
                return Response(
                    {
                        'message': 'No outlier records found',
                        'data': []
                    },
                    status=status.HTTP_200_OK
                )

            data = outliers.values(
                'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm','loc_nm', 'loc_cd', 'outlier_status', 'outlier_status_final',
                'lm_contribution_in_store', 'month', 'total_lm', 'gmv_per_day',
                'suggested_total_lm'
            )

            return Response(
                {
                    'count': outliers.count(),
                    'data': list(data)
                },
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {
                    'error': 'Internal server error',
                    'details': str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def patch(self, request):
        """Update outlier status for multiple records with all-or-nothing validation and updates"""
        # Handle both single object and array of objects
        data = request.data

        if not data:
            return Response(
                {
                    'error': 'Invalid input data',
                    'details': 'Request data cannot be empty'
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        # Step 1: Validate all objects first
        validated_objects = []
        validation_errors = []

        for index, item in enumerate(data):
            serializer = OutlierUpdateSerializer(data=item)
            if not serializer.is_valid():
                validation_errors.append({
                    'index': index,
                    'data': item,
                    'errors': serializer.errors
                })
            else:
                validated_objects.append(serializer.validated_data)

        # If any validation errors, return them all
        if validation_errors:
            return Response(
                {
                    'error': 'Validation failed for one or more records',
                    'details': validation_errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        # Step 2: Check if all records exist before updating any
        records_to_update = []
        missing_records = []

        try:
            for index, validated_data in enumerate(validated_objects):
                sub_clss_nm = validated_data['sub_clss_nm']
                loc_cd = validated_data['loc_cd']
                month = validated_data['month']

                try:
                    outlier_record = AppHbPreopt.objects.get(
                        sub_clss_nm=sub_clss_nm,
                        loc_cd=int(loc_cd),
                        month=month
                    )
                    records_to_update.append((outlier_record, validated_data))
                except AppHbPreopt.DoesNotExist:
                    missing_records.append({
                        'index': index,
                        'sub_clss_nm': sub_clss_nm,
                        'loc_cd': loc_cd,
                        'month': month
                    })
                except ValueError as e:
                    return Response(
                        {
                            'error': 'Invalid data format',
                            'details': f'loc_cd must be a valid integer at index {index}: {str(e)}'
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # If any records are missing, return error
            if missing_records:
                return Response(
                    {
                        'error': 'One or more records not found',
                        'details': missing_records
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            # Step 3: Update all records in a transaction (all-or-nothing)
            updated_records = []
            with transaction.atomic():
                for outlier_record, validated_data in records_to_update:
                    outlier_record.outlier_status = validated_data['outlier_status']
                    outlier_record.outlier_status_final = validated_data['outlier_status_final']
                    outlier_record.save(update_fields=['outlier_status', 'outlier_status_final'])

                    updated_records.append({
                        'sub_clss_nm': outlier_record.sub_clss_nm,
                        'loc_cd': outlier_record.loc_cd,
                        'outlier_status': outlier_record.outlier_status,
                        'outlier_status_final': outlier_record.outlier_status_final,
                        'month': outlier_record.month
                    })

            return Response(
                {
                    'message': f'{len(updated_records)} outlier record(s) updated successfully',
                    'count': len(updated_records),
                    'data': updated_records
                },
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {
                    'error': 'Internal server error',
                    'details': str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class FileUploadAPIView(APIView):
    """
    API endpoint to upload Excel files for different data types (sqft, mdq, cover, exclusion).
    
    - Accepts POST requests with a single Excel file and required parameters.
    - Parameters:
        - file: The Excel file to upload (multipart/form-data)
        - scenario_id: The scenario to associate the data with
        - file_type: One of ['sqft', 'mdq', 'cover', 'exclusion']
    - Validates the file structure and required columns for the specified file_type.
    - Fails the entire upload if validation fails (no partial success).
    - On success, inserts all rows into the corresponding table and returns a success message with row count.
    - On failure, returns a clear error message.
    """
    def post(self, request):
        file = request.FILES.get('file')
        scenario_id = request.data.get('scenario_id')
        file_type = request.data.get('file_type')
        print("DATA:", request.data)
        print("FILES:", request.FILES)
        if not file or not scenario_id or not file_type:
            return Response({'error': 'Missing file, scenario_id, or file_type'}, status=status.HTTP_400_BAD_REQUEST)
        file_type = file_type.lower()
        if file_type not in ['sqft', 'mdq', 'cover', 'exclusion']:
            return Response({'error': 'Invalid file_type. Must be one of: sqft, mdq, cover, exclusion'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            wb = openpyxl.load_workbook(file)
            ws = wb.active
        except Exception as e:
            return Response({'error': f'Invalid Excel file: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            print("scenario_id",scenario_id,type(scenario_id))
            scenario = ScenarioMetad.objects.get(id=int(scenario_id))
            print("scenario",scenario)
        except ScenarioMetad.DoesNotExist:
            return Response({'error': 'Invalid scenario_id'}, status=status.HTTP_400_BAD_REQUEST)
        # Validation and parsing
        if file_type == 'sqft':
            valid, msg, col_idx = validate_sqft(ws)
            if not valid:
                return Response({'error': msg}, status=status.HTTP_400_BAD_REQUEST)
            rows = parse_rows(ws, col_idx)
            file_upload = FileUploads.objects.create(file_name=file.name, file_type='sqft', uploaded_at=timezone.now())
            insert_sqft(rows, scenario, file_upload)
            print(rows)
            print("file_upload",file_upload)
            insert_sqft(rows,None, file_upload)
        elif file_type == 'mdq':
            valid, msg, col_idx = validate_mdq(ws)
            if not valid:
                return Response({'error': msg}, status=status.HTTP_400_BAD_REQUEST)
            rows = parse_rows(ws, col_idx)
            file_upload = FileUploads.objects.create(file_name=file.name, file_type='mdq', uploaded_at=timezone.now())
            insert_mdq(rows, scenario, file_upload)
        elif file_type == 'cover':
            valid, msg, col_idx = validate_cover(ws)
            if not valid:
                return Response({'error': msg}, status=status.HTTP_400_BAD_REQUEST)
            rows = parse_rows(ws, col_idx)
            file_upload = FileUploads.objects.create(file_name=file.name, file_type='cover', uploaded_at=timezone.now())
            insert_cover(rows, scenario, file_upload)
        elif file_type == 'exclusion':
            valid, msg, col_idx = validate_exclusion(ws)
            if not valid:
                return Response({'error': msg}, status=status.HTTP_400_BAD_REQUEST)
            rows = parse_rows(ws, col_idx)
            file_upload = FileUploads.objects.create(file_name=file.name, file_type='exclusion', uploaded_at=timezone.now())
            insert_exclusion(rows, scenario, file_upload)
        return Response({'message': f'Successfully imported {len(rows)} rows'}, status=status.HTTP_201_CREATED)
#also populate sqft_file_id
# mdq_file_id
# cover_file_id
# exclusion_file_id in scenario_metad table
def validate_sqft(ws):
    # Map Excel columns to model fields
    excel_to_model = {
        'Territory': 'territory_nm',
        'Store': 'loc_cd',
        'Group': 'grp_nm',
        'Dept': 'dpt_nm',
        'Class': 'clss_nm',
        'Subclass': 'sub_clss_nm',
        'Sqft': 'sqft',
    }
    header = [str(cell.value).strip() if cell.value is not None else '' for cell in next(ws.iter_rows(min_row=1, max_row=1))]
    # print(header)
    required_columns = list(excel_to_model.keys())
    if not set(required_columns).issubset(header):
        return False, f'Missing required columns: {set(required_columns) - set(header)}', None
    col_idx = {excel_to_model[col]: header.index(col) for col in required_columns}
    return True, '', col_idx

# Optionally, update parse_rows to support this mapping if needed for other file types

def validate_mdq(ws):
    required_columns = ['territory_nm', 'loc_cd', 'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm', 'mdq']
    header = [cell.value for cell in next(ws.iter_rows(min_row=1, max_row=1))]
    if not set(required_columns).issubset(header):
        return False, f'Missing required columns: {set(required_columns) - set(header)}', None
    col_idx = {col: header.index(col) for col in required_columns}
    return True, '', col_idx

def validate_cover(ws):
    required_columns = ['territory_nm', 'loc_cd', 'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm', 'mid_cover_start', 'high_cover_start', 'depth']
    header = [cell.value for cell in next(ws.iter_rows(min_row=1, max_row=1))]
    if not set(required_columns).issubset(header):
        return False, f'Missing required columns: {set(required_columns) - set(header)}', None
    col_idx = {col: header.index(col) for col in required_columns}
    return True, '', col_idx

def validate_exclusion(ws):
    required_columns = ['territory_nm', 'loc_cd', 'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm', 'item_cd']
    header = [cell.value for cell in next(ws.iter_rows(min_row=1, max_row=1))]
    if not set(required_columns).issubset(header):
        return False, f'Missing required columns: {set(required_columns) - set(header)}', None
    col_idx = {col: header.index(col) for col in required_columns}
    return True, '', col_idx

def parse_rows(ws, col_idx):
    rows = []
    for row in ws.iter_rows(min_row=2, values_only=True):
        rows.append({col: row[idx] for col, idx in col_idx.items()})
    return rows

def insert_sqft(rows, scenario, file_upload):
    try:
        for row in rows:
            SqftFileData.objects.create(
                scenario=scenario,
                file_upload=file_upload,
                territory_nm=row['territory_nm'],
                loc_cd=row['loc_cd'],
                grp_nm=row['grp_nm'],
                dpt_nm=row['dpt_nm'],
                clss_nm=row['clss_nm'],
                sub_clss_nm=row['sub_clss_nm'],
                sqft=row['sqft']
            )
    except Exception as e:
        return Response({'error': f'Failed in inserting data: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



def insert_mdq(rows, scenario, file_upload):
    for row in rows:
        MdqFileData.objects.create(
            scenario=scenario,
            file_upload=file_upload,
            territory_nm=row['territory_nm'],
            loc_cd=row['loc_cd'],
            grp_nm=row['grp_nm'],
            dpt_nm=row['dpt_nm'],
            clss_nm=row['clss_nm'],
            sub_clss_nm=row['sub_clss_nm'],
            mdq=row['mdq']
        )

def insert_cover(rows, scenario, file_upload):
    for row in rows:
        CoverFileData.objects.create(
            scenario=scenario,
            file_upload=file_upload,
            territory_nm=row['territory_nm'],
            loc_cd=row['loc_cd'],
            grp_nm=row['grp_nm'],
            dpt_nm=row['dpt_nm'],
            clss_nm=row['clss_nm'],
            sub_clss_nm=row['sub_clss_nm'],
            mid_cover_start=row['mid_cover_start'],
            high_cover_start=row['high_cover_start'],
            depth=row['depth']
        )

def insert_exclusion(rows, scenario, file_upload):
    for row in rows:
        ExclusionFileData.objects.create(
            scenario=scenario,
            file_upload=file_upload,
            territory_nm=row['territory_nm'],
            loc_cd=row['loc_cd'],
            grp_nm=row['grp_nm'],
            dpt_nm=row['dpt_nm'],
            clss_nm=row['clss_nm'],
            sub_clss_nm=row['sub_clss_nm'],
            item_cd=row['item_cd']
        )

class HealthMetricsView(APIView):
    """
    API endpoint for area, stock, sales, and productivity metrics.
    Accepts filters and reference period to calculate subclass-level metrics.
    """

    def get(self, request):
        """
        Handles GET request to return stock, area, sales, and productivity metrics grouped by GDCS (Group/Department/Class/Subclass).

        - Accepts optional filters via query params: store_id[], group[], department[], class[], sub_class[]
        - Requires ref_period with 'start' and 'end' (e.g., {'start': '2024-01', 'end': '2024-06'})
        - Calculates LM, gmv, SOH, productivity metrics per subclass for the given period
        - Returns paginated results with rankings
        """
        try:
            # ref_period = request.data.get('ref_period')
            ref_period = {'start': '202411', 'end': '202501'} #dummy value for testing
            if not ref_period or 'start' not in ref_period or 'end' not in ref_period:
                return Response(
                    {
                        'error': 'Invalid request body',
                        'details': 'ref_period with start and end months is required'
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )

            start_month = datetime.strptime(ref_period['start'], '%Y%m')
            end_month = datetime.strptime(ref_period['end'], '%Y%m')
            months_in_period = (relativedelta(end_month, start_month).months +
                                (relativedelta(end_month, start_month).years * 12) + 1)
            days_in_period = months_in_period * 30  # approximate month length
            queryset = AppHbPreopt.objects.all()
            queryset = self._apply_filters(queryset, request.query_params)
            aggregated_data = self._calculate_aggregations(queryset, months_in_period, days_in_period)

            if not aggregated_data:
                return Response(
                    {
                        'message': 'No data found for the specified criteria',
                        'data': [],
                        'pagination': {
                            'count': 0,
                            'total_pages': 0,
                            'current_page': 1,
                            'page_size': 10
                        }
                    },
                    status=status.HTTP_200_OK
                )

            ranked_data = self._add_rankings(aggregated_data)
            paginator = HeallthMetricsPagination()
            paginated_data = paginator.paginate_queryset(ranked_data, request)
            return paginator.get_paginated_response(paginated_data)

        except Exception as e:
            return Response(
                {
                    'error': 'Internal server error',
                    'details': str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _apply_filters(self, queryset, query_params):
        store_ids = query_params.getlist('store_id[]')
        if store_ids:
            try:
                store_ids = [int(sid) for sid in store_ids]
                queryset = queryset.filter(loc_cd__in=store_ids)
            except ValueError:
                pass

        groups = query_params.getlist('group[]')
        if groups:
            queryset = queryset.filter(grp_nm__in=groups)

        departments = query_params.getlist('department[]')
        if departments:
            queryset = queryset.filter(dpt_nm__in=departments)

        classes = query_params.getlist('class[]')
        if classes:
            queryset = queryset.filter(clss_nm__in=classes)

        sub_classes = query_params.getlist('sub_class[]')
        if sub_classes:
            queryset = queryset.filter(sub_clss_nm__in=sub_classes)

        return queryset

    def _calculate_aggregations(self, queryset, months_in_period, days_in_period):
        aggregated = queryset.values(
            'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm'
        ).annotate(
            total_lm_sum=Sum('total_lm'),
            total_optn_cnt_sum=Sum('mnth_avg_optn_cnt'),
            total_soh_sum=Sum('mnth_avg_soh'),
            total_net_sls_sum=Sum('net_sls_amt'),
            total_gmv_sum=Sum('gmv'),
            avg_lm_contribution=Avg('lm_contribution_in_store')
        )

        results = []
        for item in aggregated:
            lm = (item['total_lm_sum'] / months_in_period) if months_in_period else 0
            option_count = (item['total_optn_cnt_sum'] / months_in_period) if months_in_period else 0
            soh = (item['total_soh_sum'] / months_in_period) if months_in_period else 0
            rev = (item['total_net_sls_sum'] / months_in_period) if months_in_period else 0
            gmv = (item['total_gmv_sum'] / months_in_period) if months_in_period else 0

            rev_per_day = (item['total_net_sls_sum'] / days_in_period) if days_in_period else 0
            gmv_per_day = (item['total_gmv_sum'] / days_in_period) if days_in_period else 0

            option_density = (option_count / lm) if lm else 0
            stock_density = (soh / lm) if lm else 0
            gmv_per_lm_per_day = (gmv_per_day / lm) if lm else 0
            rev_per_lm_per_day = (rev_per_day / lm) if lm else 0

            result = {
                'grp_nm': item['grp_nm'],
                'dpt_nm': item['dpt_nm'],
                'clss_nm': item['clss_nm'],
                'sub_clss_nm': item['sub_clss_nm'],
                'lm': round(lm, 2),
                'lm_contribution_in_store': round(item['avg_lm_contribution'] or 0, 2),
                'option_count': round(option_count, 2),
                'option_density': round(option_density, 2),
                'soh': round(soh, 2),
                'stock_density': round(stock_density, 2),
                'rev': round(rev, 2),
                'gmv': round(gmv, 2),
                'rev_per_day': round(rev_per_day, 2),
                'gmv_per_day': round(gmv_per_day, 2),
                'gmv_per_lm_per_day': round(gmv_per_lm_per_day, 2),
                'rev_per_lm_per_day': round(rev_per_lm_per_day, 2),
                '_lm_raw': lm,
                '_gmv_per_lm_per_day_raw': gmv_per_lm_per_day,
                '_rev_per_lm_per_day_raw': rev_per_lm_per_day,
            }
            results.append(result)

        return results

    def _add_rankings(self, data):
        data_sorted_by_lm = sorted(data, key=lambda x: x['_lm_raw'] or 0, reverse=True)
        for rank, item in enumerate(data_sorted_by_lm, 1):
            item['lm_rank'] = rank

        data_sorted_by_rev = sorted(data, key=lambda x: x['_rev_per_lm_per_day_raw'] or 0, reverse=True)
        for rank, item in enumerate(data_sorted_by_rev, 1):
            item['rev_per_lm_per_day_rank'] = rank

        data_sorted_by_gmv = sorted(data, key=lambda x: x['_gmv_per_lm_per_day_raw'] or 0, reverse=True)
        for rank, item in enumerate(data_sorted_by_gmv, 1):
            item['gmv_per_lm_per_day_rank'] = rank

        for item in data:
            item.pop('_lm_raw', None)
            item.pop('_gmv_per_lm_per_day_raw', None)
            item.pop('_rev_per_lm_per_day_raw', None)

        return data

class ScenarioList(APIView):
    def post(self, request):
        try:
            # Get pagination params
            page = int(request.data.get('page', 1))
            page_size = int(request.data.get('page_size', 10))
            search = request.data.get('search', '').strip()
            status_check = request.data.get('status', '')
            print("status",status_check)
            offset = (page - 1) * page_size

            base_query = "FROM scenario_metad sm JOIN scenario_status ss on sm.id = ss.scenario_id where 1=1"
            params = []
            base_query +=" AND ss.STATUS = %s"
            params.append(f"{status_check}")

            # Add search filter if provided
            if search:

                base_query += " AND name LIKE %s"
                params.append(f"%{search}%")

            # Total count query
            count_query = f"SELECT COUNT(*) {base_query}"

            # Data query with LIMIT/OFFSET
            data_query = f"""
                SELECT sm.id,sm.name,sm.eval_type,sm.event_name,sm.eval_start,sm.eval_end,ss.STATUS
                {base_query}
                ORDER BY id DESC
                LIMIT %s OFFSET %s
            """

            # Execute queries
            with connection.cursor() as cursor:
                cursor.execute(count_query, params)
                total_count = cursor.fetchone()[0]

                cursor.execute(data_query, params + [page_size, offset])
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()
                

            # Convert to list of dict
            data = [dict(zip(columns, row)) for row in rows]


            return Response({
                "count": total_count,
                "total_pages":math.ceil(total_count / page_size),
                "page": page,
                "page_size": page_size,
                "results": data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
class OutlierAPIView(APIView):
    def get(self, request):
        sub_clss_nm = request.query_params.get('subclass')
        loc_cd = request.query_params.get('store')
        filters = {}
        if sub_clss_nm:
            filters['sub_clss_nm'] = sub_clss_nm
        if loc_cd:
            filters['loc_cd'] = loc_cd
        outliers = AppHbPreopt.objects.filter(**filters)
        # Only return the required fields
        data = outliers.values(
            'lm_contribution_in_store',  # lm_contribution_in_store
            'sub_clss_nm',               # sub_clss_nm
            'loc_cd',                    # loc_cd
            'month',                     # month
            'total_lm',                  # total_lm
            'gmv_per_day',               # gmv_per_day
            'suggested_total_lm'         # suggested_total_lm
        )
        return Response(list(data))

    def put(self, request):
        pass


class testAndControlStore(APIView):

    def post(self, request):
        # Step 1: Validate input
        input_serializer = testAndControlStoreSerializer(data=request.data)
        if not input_serializer.is_valid():
            return Response(input_serializer.errors, status=400)

        validated = input_serializer.validated_data
        concept = validated["concept"]
        territory = validated["territory"]

        # Step 2: Resolve model from map
        table_name = f"de_{concept.lower()}_{territory.lower()}_cluster"
        ClusterModel = CLUSTER_MODEL_MAP.get(table_name)
        if not ClusterModel:
            return Response({"error": f"Unsupported cluster table: {table_name}"}, status=400)

        # Step 3: Fetch data
        try:
            filtered_data = ClusterModel.objects.filter()
        except Exception as e:
            return Response({"error": f"Database error: {str(e)}"}, status=500)

        return Response(list(filtered_data.values()))

    
class RunOutliers(APIView):
    def try_convert_decimal(val):
        try:
            return float(Decimal(str(val).strip()))
        except (InvalidOperation, ValueError, TypeError):
            return None  # or 0.0 or np.nan based on your use case
    def post(self, request):
        import time
        start_time = time.time()
        
        # Validate request data
        serializer = OutlierDetectionRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "Invalid request data", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        scenario_id = validated_data['scenario_id']
        concept = validated_data['concept']
        territory = validated_data['territory']
        user_id = validated_data['user_id']
        
        try:
            with transaction.atomic():
                # Get model references
                cluster_table_name = f"app_{concept.lower()}_str_cluster"
                preopt_table_name = f"de_{concept.lower()}_{territory.lower()}_preopt"
                app_preopt_table_name = f"app_{concept.lower()}_preopt"
                
                AppClusterModel = APP_CLUSTER_MODEL_MAP.get(cluster_table_name)
                DePreOptModel = DE_PREOPTIMIZATION_MODEL_MAP.get(preopt_table_name)
                AppPreoptModel = APP_PREOPTIMIZATION_MODEL_MAP.get(app_preopt_table_name)
                
                if not all([AppClusterModel, DePreOptModel, AppPreoptModel]):
                    return Response(
                        {"error": "Required models not found in model maps"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                # Step 1: Get cluster data
                cluster_queryset = AppClusterModel.objects.filter(
                    scenario_id=scenario_id
                ).values('loc_cd', 'cluster_num').distinct()
                
                if not cluster_queryset.exists():
                    return Response(
                        {"error": f"No cluster data found for scenario_id: {scenario_id}"},
                        status=status.HTTP_404_NOT_FOUND
                    )
                
                # Convert to DataFrame
                cluster_df = pd.DataFrame(list(cluster_queryset))
                cluster_df.columns = ['loc_cd', 'cluster_num']
                
                # Get all stores in these clusters
                loc_codes = cluster_df['loc_cd'].unique().tolist()
                
                # Step 2: Get preopt data for all stores in clusters
                preopt_queryset = DePreOptModel.objects.filter(
                    loc_cd__in=loc_codes
                ).values()
                
                if not preopt_queryset.exists():
                    return Response(
                        {"error": "No preopt data found for the given stores"},
                        status=status.HTTP_404_NOT_FOUND
                    )
                
                # Convert to DataFrame
                df = pd.DataFrame(list(preopt_queryset))
                
                # Convert column names to uppercase for consistency
                df.columns = df.columns.str.upper()
                cluster_df.columns = cluster_df.columns.str.upper()
                
                # Convert Decimal fields to float
                for col in df.columns:
                    if df[col].apply(lambda x: isinstance(x, Decimal)).any():
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                
                # Step 3: Initial Filters
                df = df[df['gmv'].notna()]
                
                if df.empty:
                    return Response(
                        {"error": "No valid data after filtering gmv nulls"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                # Step 4: Join with cluster info
                df = df.merge(cluster_df, on='loc_cd', how='left')
                
                # Remove rows where cluster info is missing
                df = df[df['cluster_num'].notna()]
                
                if df.empty:
                    return Response(
                        {"error": "No data after joining with cluster information"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                # Step 5: Z-score windows
                gdcs_cols = ['cluster_num', 'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm']
                
                # Check if required columns exist
                missing_cols = [col for col in gdcs_cols if col not in df.columns]
                if missing_cols:
                    return Response(
                        {"error": f"Missing required columns: {missing_cols}"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                grouped = df.groupby(gdcs_cols)
                
                # gmv per day calculations
                df['GMV_PER_DAY_MEAN'] = grouped['gmv_per_day'].transform('mean')
                df['GMV_PER_DAY_STD'] = grouped['gmv_per_day'].transform('std')
                df['GMV_PER_DAY_Z'] = np.where(
                    df['GMV_PER_DAY_STD'] != 0,
                    (df['gmv_per_day'] - df['GMV_PER_DAY_MEAN']) / df['GMV_PER_DAY_STD'],
                    0
                )
                
                # LM contribution calculations
                df['LM_CONT_MEAN'] = grouped['lm_contribution_in_store'].transform('mean')
                df['LM_CONT_STD'] = grouped['lm_contribution_in_store'].transform('std')
                df['LM_CONT_Z'] = np.where(
                    df['LM_CONT_STD'] != 0,
                    (df['lm_contribution_in_store'] - df['LM_CONT_MEAN']) / df['LM_CONT_STD'],
                    0
                )
                
                # Step 6: Outlier classification
                df['LM_OUTLIER'] = df['LM_CONT_Z'].abs() > 2
                df['GMV_OUTLIER'] = df['GMV_PER_DAY_Z'].abs() < 1
                
                df['outlier_status'] = np.select(
                    [
                        df['LM_OUTLIER'] & df['GMV_OUTLIER'],
                        df['LM_OUTLIER'] & ~df['GMV_OUTLIER']
                    ],
                    ['MAJOR_OUTLIER', 'MINOR_OUTLIER'],
                    default='NORMAL'
                )
                
                # Step 7: Imputation using rolling past/next 3
                df = df.sort_values(['loc_cd', 'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm', 'month'])
                
                # Define grouping key
                group_cols = ['loc_cd', 'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm']
                
                df['VALID_LM'] = np.where(
                    (df['outlier_status'] != 'MAJOR_OUTLIER') & (df['total_lm'] > 0), 
                    df['total_lm'], 
                    np.nan
                )
                
                # Rolling past and next 3
                def get_avg_past_next(group):
                    group = group.copy()
                    group['avg_last_3'] = group['VALID_LM'].shift(1).rolling(3, min_periods=1).mean()
                    group['avg_next_3'] = group['VALID_LM'].shift(-1).rolling(3, min_periods=1).mean()
                    return group
                
                df = df.groupby(group_cols).apply(get_avg_past_next).reset_index(drop=True)
                
                # Final Imputation - Update total_lm instead of creating IMPUTED_LM
                df['total_lm'] = np.where(
                    df['outlier_status'] == 'MAJOR_OUTLIER',
                    df['avg_last_3'].combine_first(df['avg_next_3']).fillna(0).round(2),
                    df['total_lm']
                )
                
                # Add suggested_total_lm (same as updated total_lm)
                df['suggested_total_lm'] = df['total_lm']
                
                # Step 8: Prepare data for insertion/update in AppPreoptModel
                current_time = timezone.now()
                
                # Filter data to only include stores from cluster model
                df_filtered = df[df['loc_cd'].isin(loc_codes)]
                
                # Delete existing records for these stores to avoid duplicates
                AppPreoptModel.objects.filter(LOC_CD__in=loc_codes).delete()
                
                def safe_decimal(value, default=0):
                    try:
                        if pd.isna(value) or value is None:
                            return Decimal(str(default))
                        return Decimal(str(float(value)))
                    except (ValueError, TypeError, InvalidOperation):
                        return Decimal(str(default))
                # Prepare bulk insert data
                app_preopt_records = []
                for _, row in df_filtered.iterrows():
                    record = AppPreoptModel(
                        loc_cd=str(row['loc_cd']),
                        loc_nm=row.get('loc_nm', ''),
                        rgn_nm=row.get('rgn_nm', ''),
                        grp_nm=row.get('grp_nm', ''),
                        dpt_nm=row.get('dpt_nm', ''),
                        clss_nm=row.get('clss_nm', ''),
                        sub_clss_nm=row.get('sub_clss_nm', ''),
                        month=str(row.get('month', '')),
                        mnth_avg_soh=safe_decimal(str(row.get('mnth_avg_soh', 0))),
                        mnth_avg_itm_cnt=safe_decimal(str(row.get('mnth_avg_itm_cnt', 0))),
                        mnth_avg_optn_cnt=safe_decimal(str(row.get('mnth_avg_optn_cnt', 0))),
                        mnth_end_soh=safe_decimal(str(row.get('mnth_end_soh', 0))),
                        mnth_end_itm_cnt=int(row.get('mnth_end_itm_cnt', 0)),
                        mnth_end_optn_cnt=int(row.get('mnth_end_optn_cnt', 0)),
                        net_sls_amt=safe_decimal(str(row.get('net_sls_amt', 0))),
                        rtl_qty=safe_decimal(str(row.get('rtl_qty', 0))),
                        gmv=safe_decimal(str(row.get('gmv', 0))),
                        inv_cnt=int(row.get('inv_cnt', 0)),
                        cust_cnt=int(row.get('cust_cnt', 0)),
                        str_visits=int(row.get('str_visits', 0)),
                        str_cust_cnt=int(row.get('str_cust_cnt', 0)),
                        cust_pen=safe_decimal(str(row.get('cust_pen', 0))),
                        spc=safe_decimal(str(row.get('spc', 0))),
                        margin_perc=safe_decimal(str(row.get('margin_perc', 0))),
                        asp=safe_decimal(str(row.get('asp', 0))),
                        sls_per_inv=safe_decimal(str(row.get('sls_per_inv', 0))),
                        units_per_inv=safe_decimal(str(row.get('units_per_inv', 0))),
                        ros=safe_decimal(str(row.get('ros', 0))),
                        cover=safe_decimal(str(row.get('cover', 0))),
                        total_lm=safe_decimal(str(row.get('total_lm', 0))),
                        gmv_per_day=safe_decimal(str(row.get('gmv_per_day', 0))),
                        gmv_per_lm=safe_decimal(str(row.get('gmv_per_lm', 0))),
                        lm_contribution_in_store=safe_decimal(str(row.get('lm_contribution_in_store', 0))),
                        outlier_status=row.get('outlier_status', 'NORMAL'),
                        suggested_total_lm=safe_decimal(str(row.get('suggested_total_lm', 0))),
                        last_update_dt_tm=current_time,
                        scenario_id=scenario_id
                    )
                    app_preopt_records.append(record)
                
                # Bulk insert records
                AppPreoptModel.objects.bulk_create(app_preopt_records, batch_size=1000)
                
                # Calculate statistics
                total_processed = len(df_filtered)
                major_outliers = len(df_filtered[df_filtered['outlier_status'] == 'MAJOR_OUTLIER'])
                minor_outliers = len(df_filtered[df_filtered['outlier_status'] == 'MINOR_OUTLIER'])
                total_outliers = major_outliers + minor_outliers
                
                execution_time = time.time() - start_time
                
                response_data = {
                    "message": "Outlier detection and imputation completed successfully",
                    "processed_stores": total_processed,
                    "outliers_found": total_outliers,
                    "major_outliers": major_outliers,
                    "minor_outliers": minor_outliers,
                    "execution_time": round(execution_time, 2)
                }
                
                response_serializer = OutlierDetectionResponseSerializer(data=response_data)
                if response_serializer.is_valid():
                    return Response(response_serializer.data, status=status.HTTP_200_OK)
                else:
                    return Response(response_data, status=status.HTTP_200_OK)
                
        except Exception as e:
            # logger.error(f"Error in outlier detection: {str(e)}")
            return Response(
                {"error": "An error occurred during outlier detection", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            
class insertTestControlStr(APIView):
    def post(self, request):
        import time
        start_time = time.time()
        
        # Validate request data
        serializer = InsertTestControlStrRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "Invalid request data", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        scenario_id = validated_data['scenario_id']
        store_codes = validated_data['store_codes']  # Already validated as dict
        
        try:
            with transaction.atomic():
                # Check if scenario exists
                try:
                    scenario = ScenarioMetad.objects.get(id=scenario_id)
                except ScenarioMetad.DoesNotExist:
                    return Response(
                        {"error": f"Scenario with id {scenario_id} not found"},
                        status=status.HTTP_404_NOT_FOUND
                    )
                
                # Update the scenario's loc_cd field with all store codes
                current_time = timezone.now()
                scenario.loc_cd = store_codes
                scenario.updated_at = current_time
                scenario.save()
                
                execution_time = time.time() - start_time
                
                response_data = {
                    "message": "Test control store data inserted successfully",
                    "scenario_id": scenario_id,
                    "store_mapping": store_codes,
                    "execution_time": round(execution_time, 2)
                }
                
                response_serializer = InsertTestControlStrResponseSerializer(data=response_data)
                if response_serializer.is_valid():
                    return Response(response_serializer.data, status=status.HTTP_200_OK)
                else:
                    return Response(response_data, status=status.HTTP_200_OK)
                
        except Exception as e:
            return Response(
                {"error": "An error occurred while inserting test control store data", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        
class getDataSummary(APIView):
    def post(self, request):
        # Validate request data
        print(request.data)
        serializer = GraphDataRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "Invalid request data", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        print(serializer.validated_data)
        validated_data = serializer.validated_data
        concept = validated_data['concept']
        scenario_id = validated_data['scenario_id']
        group_filter = validated_data.get('group', [])
        department_filter = validated_data.get('department', [])
        class_filter = validated_data.get('class', [])
        sub_class_filter = validated_data.get('sub_class', [])
        from_month = validated_data['from_month']
        to_month = validated_data['to_month']
        
        try:
            # Get model reference
            app_preopt_table_name = f"app_{concept.lower()}_preopt"
            AppPreoptModel = APP_PREOPTIMIZATION_MODEL_MAP.get(app_preopt_table_name)
            
            if not AppPreoptModel:
                return Response(
                    {"error": f"Model not found for concept: {concept}"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Convert month format for filtering (MM/YYYY to YYYY-MM or your DB format)
            from_month_db = self.convert_month_format(from_month)
            to_month_db = self.convert_month_format(to_month)
            
            # Build base query
            query = AppPreoptModel.objects.filter(scenario_id=scenario_id)
            
            # Add month range filter
            query = query.filter(month__gte=from_month_db, month__lte=to_month_db)
            
            # Determine aggregation level and apply filters
            aggregation_level, group_by_fields = self.determine_aggregation_level(
                group_filter, department_filter, class_filter, sub_class_filter
            )
            
            # Apply hierarchy filters
            if group_filter:
                query = query.filter(GRP_NM__in=group_filter)
            if department_filter:
                query = query.filter(DPT_NM__in=department_filter)
            if class_filter:
                query = query.filter(CLSS_NM__in=class_filter)
            if sub_class_filter:
                query = query.filter(SUB_CLSS_NM__in=sub_class_filter)
            
            # Group by month and hierarchy level
            if aggregation_level == 'store':
                # Most detailed level - group by store and month
                group_by_fields = ['month', 'loc_cd', 'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm']
            else:
                group_by_fields = ['month'] + group_by_fields
            
            # Aggregate data
            aggregated_data = (query
                .values(*group_by_fields)
                .annotate(
                    total_lm=Sum('total_lm'),
                    total_gmv=Sum('gmv'),
                    avg_gmv_per_lm=Avg('gmv_per_lm')
                )
                .order_by('month')
            )
            
            # Calculate productivity (gmv per LM)
            data_points = []
            for item in aggregated_data:
                productivity = float(item['total_gmv'] / item['total_lm']) if item['total_lm'] and item['total_lm'] > 0 else 0
                
                data_point = {
                    'month': item['month'],
                    'total_lm': float(item['total_lm'] or 0),
                    'productivity': round(productivity, 2),
                    'gmv_per_lm': float(item['avg_gmv_per_lm'] or 0)
                }
                
                # Add hierarchy information based on aggregation level
                if 'grp_nm' in item:
                    data_point['group_name'] = item['grp_nm']
                if 'dpt_nm' in item:
                    data_point['department_name'] = item['dpt_nm']
                if 'clss_nm' in item:
                    data_point['class_name'] = item['clss_nm']
                if 'sub_clss_nm' in item:
                    data_point['sub_class_name'] = item['sub_clss_nm']
                
                data_points.append(data_point)
            
            response_data = {
                "message": "Graph data retrieved successfully",
                "data_points": data_points,
                "total_records": len(data_points),
                "aggregation_level": aggregation_level,
                "filters_applied": {
                    "group": group_filter,
                    "department": department_filter,
                    "class": class_filter,
                    "sub_class": sub_class_filter,
                    "from_month": from_month,
                    "to_month": to_month
                }
            }
            
            response_serializer = GraphDataResponseSerializer(data=response_data)
            if response_serializer.is_valid():
                return Response(response_serializer.data, status=status.HTTP_200_OK)
            else:
                return Response(response_data, status=status.HTTP_200_OK)
                
        except Exception as e:
            return Response(
                {"error": "An error occurred while retrieving graph data", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def determine_aggregation_level(self, group_filter, department_filter, class_filter, sub_class_filter):
        """Determine the appropriate aggregation level based on filters"""
        if sub_class_filter:
            return 'sub_class', ['grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm']
        elif class_filter:
            return 'class', ['grp_nm', 'dpt_nm', 'clss_nm']
        elif department_filter:
            return 'department', ['grp_nm', 'dpt_nm']
        elif group_filter:
            return 'group', ['grp_nm']
        else:
            return 'group', ['grp_nm']  # Default to group level
    
    def convert_month_format(self, month_str):
        """Convert MM/YYYY to your database month format"""
        # Adjust this based on your actual database month format
        # If month field stores as 'YYYY-MM', 'MM/YYYY', or just 'MM' etc.
        year, month = month_str.split('-')
        return f"{year}{month.zfill(2)}"  # Assuming YYYY-MM format in DB
        # Or return month_str if it matches your DB format exactly
        
class getMetricGraph(APIView):
    def post(self, request):
        # Validate request data
        print(request.data)
        serializer = MetricGraphRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "Invalid request data", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        print(serializer.validated_data)
        validated_data = serializer.validated_data
        concept = validated_data['concept']
        scenario_id = validated_data['scenario_id']
        loc_cd = validated_data['loc_cd']
        metric_field = validated_data['metric']  # This is now the DB field name
        
        # Get original metric name for display
        reverse_mapping = {
            'units_per_inv': 'Units Per Invoice',
            'cust_pen': 'Customer Penetration',
            'cover': 'Cover',
            'margin_perc': 'Margin',
            'gmv': 'Productivity',
            'asp': 'Average Selling Price'
        }
        metric_display_name = reverse_mapping.get(metric_field, metric_field)
        
        try:
            # Get model reference
            app_preopt_table_name = f"app_{concept.lower()}_preopt"
            AppPreoptModel = APP_PREOPTIMIZATION_MODEL_MAP.get(app_preopt_table_name)
            
            if not AppPreoptModel:
                return Response(
                    {"error": f"Model not found for concept: {concept}"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get the latest month for the specific store and scenario
            latest_month_query = AppPreoptModel.objects.filter(
                scenario_id=scenario_id,
                loc_cd=loc_cd
            ).aggregate(latest_month=Max('month'))
            
            latest_month = latest_month_query['latest_month']
            
            if not latest_month:
                return Response(
                    {"error": f"No data found for store {loc_cd} in scenario {scenario_id}"},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Get store name
            store_info = AppPreoptModel.objects.filter(
                scenario_id=scenario_id,
                loc_cd=loc_cd,
                month=latest_month
            ).values('loc_nm').first()
            
            store_name = store_info['loc_nm'] if store_info else f"Store {loc_cd}"
            
            # Get subclass data for the latest month
            # Use **{metric_field: Sum} to dynamically create the annotation
            annotation_dict = {f'metric_total': Sum(metric_field)}
            
            subclass_data = (AppPreoptModel.objects
                .filter(
                    scenario_id=scenario_id,
                    loc_cd=loc_cd,
                    month=latest_month
                )
                .values('sub_clss_nm')
                .annotate(**annotation_dict)
                .order_by('-metric_total')
            )
            
            if not subclass_data.exists():
                return Response(
                    {"error": f"No subclass data found for store {loc_cd} in month {latest_month}"},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Convert to list and get top 5 and bottom 5
            subclass_list = list(subclass_data)
            total_subclasses = len(subclass_list)
            
            # Top 5 (highest values)
            top_5_data = []
            for i, item in enumerate(subclass_list[:5], 1):
                top_5_data.append({
                    'sub_class_name': item['sub_clss_nm'],
                    'metric_value': float(item['metric_total'] or 0),
                    'rank': i,
                    'category': 'top'
                })
            
            # Bottom 5 (lowest values) - get last 5 and reverse for proper ranking
            bottom_5_data = []
            if total_subclasses > 5:
                bottom_5_list = subclass_list[-5:]
                for i, item in enumerate(reversed(bottom_5_list), 1):
                    bottom_5_data.append({
                        'sub_class_name': item['sub_clss_nm'],
                        'metric_value': float(item['metric_total'] or 0),
                        'rank': i,
                        'category': 'bottom'
                    })
            
            response_data = {
                "message": "Metric graph data retrieved successfully",
                "store_code": loc_cd,
                "store_name": store_name,
                "latest_month": latest_month,
                "metric": metric_field,
                "metric_display_name": metric_display_name,
                "top_5": top_5_data,
                "bottom_5": bottom_5_data,
                "total_subclasses": total_subclasses
            }
            
            response_serializer = MetricGraphResponseSerializer(data=response_data)
            if response_serializer.is_valid():
                return Response(response_serializer.data, status=status.HTTP_200_OK)
            else:
                return Response(response_data, status=status.HTTP_200_OK)
                
        except Exception as e:
            return Response(
                {"error": "An error occurred while retrieving metric graph data", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class getAllGDCSdata(APIView):
    """
    Optimized API to get distinct GDCS data with specific fields only
    """
    
    def post(self, request):
        try:
            # Validate request data
            request_serializer = GDCSDataRequestSerializer(data=request.data)
            if not request_serializer.is_valid():
                return Response({
                    'success': False,
                    'message': 'Invalid request data',
                    'errors': request_serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = request_serializer.validated_data
            concept = validated_data['concept']
            scenario_id = validated_data['scenario_id']
            
            # Get the appropriate model based on concept
            app_preopt_table_name = f"app_{concept.lower()}_preopt"
            AppPreoptModel = APP_PREOPTIMIZATION_MODEL_MAP.get(app_preopt_table_name)
            
            if not AppPreoptModel:
                return Response({
                    'success': False,
                    'message': f'No model found for concept: {concept}',
                    'data': []
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Query for distinct GDCS combinations (Location, Group, Department, Class, Sub-Class)
            gdcs_data = AppPreoptModel.objects.filter(
                scenario_id=scenario_id
            ).values(
                'loc_cd', 'loc_nm', 'rgn_nm', 'territory_nm',
                'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm'
            ).distinct().order_by('loc_cd', 'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm')
            
            # Convert queryset to list
            gdcs_list = list(gdcs_data)
            
            return Response({
                'success': True,
                'message': 'GDCS data retrieved successfully',
                'gdcs_data': gdcs_list,
                'total_records': len(gdcs_list)
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'success': False,
                'message': 'Internal server error occurred',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class getOptimizerDetails(APIView):
    def post(self, request):
        try:
            # Get scenario_id from request body
            data = json.loads(request.body)
            scenario_id = data.get('scenario_id')
            
            if not scenario_id:
                return Response({
                    'success': False,
                    'message': 'scenario_id is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Get scenario details
            scenario = ScenarioMetad.objects.get(id=scenario_id)
            
            # Prepare response data
            optimizer_details = {
                'id': scenario.id,
                'name': scenario.name,
                'user_id': scenario.user_id,
                'season_type': scenario.season_type,
                'eval_type': scenario.eval_type,
                'event_name': scenario.event_name,
                'eval_start': scenario.eval_start,
                'eval_end': scenario.eval_end,
                'ref_start': scenario.ref_start,
                'ref_end': scenario.ref_end,
                'CNCPT_NM': scenario.CNCPT_NM,
                'TERRITORY_NM': scenario.TERRITORY_NM,
                'metric': scenario.metric,
                'sqft_file_id': scenario.sqft_file_id,
                'mdq_file_id': scenario.mdq_file_id,
                'cover_file_id': scenario.cover_file_id,
                'exclusion_file_id': scenario.exclusion_file_id,
                'loc_cd': scenario.loc_cd,
                'created_by': scenario.created_by,
                'updated_by': scenario.updated_by,
                'created_at': scenario.created_at,
                'updated_at': scenario.updated_at,
            }
            
            return Response({
                'success': True,
                'data': optimizer_details
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'An error occurred: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)